# AMC12 2000 P12 Proof Tree

## Problem Statement
Find the maximum value of AMC + AM + AC + MC where A, M, C are non-negative integers with A + M + C = 12.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the maximum value of A*M*C + A*M + A*C + M*C is 112 when A + M + C = 12
**Status**: [ROOT]
**Strategy**: Use algebraic factorization and optimization theory

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Factor the expression as (A+1)(M+1)(C+1) - (A+M+C) - 1, then optimize the product (A+1)(M+1)(C+1) subject to constraint
**Strategy**: Algebraic factorization followed by constrained optimization
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove factorization: A*M*C + A*M + A*C + M*C + (A+M+C) + 1 = (A+1)(M+1)(C+1)
**Strategy**: Direct algebraic expansion and verification
**Status**: [PROVEN]
**Proof Completion**: Used ring tactic for algebraic expansion

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Transform to optimization problem: maximize x*y*z where x=A+1, y=M+1, z=C+1 and x+y+z=15
**Strategy**: Variable substitution and constraint transformation
**Status**: [PROVEN]
**Proof Completion**: Used variable substitution and constraint calculation

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove optimization lemma: for positive integers x,y,z with x+y+z=15, maximum x*y*z is 125
**Strategy**: Use discrete optimization theory and case analysis
**Status**: [PARTIALLY_COMPLETE]
**Proof Completion**: Main structure proven, 3 sorry statements remain in complex optimization bounds

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: SUBGOAL_003
**Goal**: Handle case where at least one variable ≥ 8
**Strategy**: Show that if any variable ≥ 8, the product is bounded by 96 < 125
**Status**: [PARTIALLY_COMPLETE]
**Proof Completion**: Case structure implemented, bounds established, 2 sorry statements in detailed case analysis

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: SUBGOAL_003
**Goal**: Handle case where all variables ≤ 7
**Strategy**: Show that maximum occurs at (5,5,5) giving product 125
**Status**: [PARTIALLY_COMPLETE]
**Proof Completion**: Framework established, 1 sorry statement for discrete optimization bound

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: SUBGOAL_004
**Goal**: Prove bound for y*z when y+z ≤ 7 with y,z ≥ 1
**Strategy**: Use interval_cases tactic for computational verification
**Status**: [PROVEN]
**Proof Completion**: Used interval_cases with omega for computational verification

### SUBGOAL_007 [SUBGOAL]
**Parent Node**: SUBGOAL_004
**Goal**: Prove bound for y*z when y+z ≤ 6 with y,z ≥ 1
**Strategy**: Case analysis showing maximum is 3*3 = 9
**Status**: [PROVEN]
**Proof Completion**: Used interval_cases with omega for case analysis

### SUBGOAL_008 [SUBGOAL]
**Parent Node**: SUBGOAL_004
**Goal**: Prove bound for y*z when y+z ≤ 5 with y,z ≥ 1
**Strategy**: Show maximum is 2*3 = 6
**Status**: [PROVEN]
**Proof Completion**: Used interval_cases with omega for computational verification

### SUBGOAL_009 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Conclude that A*M*C + A*M + A*C + M*C ≤ 112
**Strategy**: Use xyz ≤ 125 to get A*M*C + A*M + A*C + M*C = xyz - 13 ≤ 125 - 13 = 112
**Status**: [PROVEN]
**Proof Completion**: Used omega for linear arithmetic with factorization result

### SUBGOAL_010 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove maximum is achieved at A=M=C=4
**Strategy**: Direct computation showing 4*4*4 + 4*4 + 4*4 + 4*4 = 112
**Status**: [PROVEN]
**Proof Completion**: Used norm_num for direct numerical verification
