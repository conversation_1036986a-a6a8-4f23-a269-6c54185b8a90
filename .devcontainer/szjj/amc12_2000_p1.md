# AMC12 2000 P1 Proof Tree

## Problem Statement
Find three distinct positive integers whose product is 2001 and whose sum is as large as possible.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the maximum sum of three distinct positive integers with product 2001 is 671
**Strategy**: Computational verification approach using factorization of 2001

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Factor 2001 = 3 × 23 × 29
2. Enumerate all possible factorizations into three distinct positive integers
3. Compute sums for each valid factorization
4. Prove that 671 is the maximum sum
**Strategy**: Exhaustive case analysis based on prime factorization

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove that 2001 = 3 × 23 × 29
**Strategy**: Direct computation using norm_num
**Status**: [PROVEN]
**Proof Completion**: Uses norm_num tactic for arithmetic verification

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Enumerate all valid factorizations of 2001 into three distinct positive integers
**Strategy**: Systematic enumeration based on divisors of 2001
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove that the only valid triples are (1,3,667), (1,23,87), (1,29,69) and their permutations
**Strategy**: Computational verification of all combinations
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove that sums are 671, 111, 99 respectively
**Strategy**: Direct arithmetic computation
**Status**: [PROMISING]

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove that 671 is the maximum among {671, 111, 99}
**Strategy**: Direct comparison using norm_num
**Status**: [PROMISING]

### SUBGOAL_006 [DEAD_END]
**Parent Node**: SUBGOAL_003
**Goal**: Complete the sorry in optimization_2001 lemma - prove that any triple (x,y,z) with x*y*z=2001 has sum in {671, 111, 99}
**Strategy**: Use computational verification with Nat.divisors to enumerate all valid triples and prove exhaustiveness
**Status**: [DEAD_END]
**Failure Reason**: Complex case analysis leads to compilation errors and overly verbose proof structure. The approach with exhaustive divisor enumeration creates too many nested cases that are difficult to manage.

### SUBGOAL_006_ALT [DEAD_END]
**Parent Node**: SUBGOAL_003
**Goal**: Complete the sorry in optimization_2001 lemma - prove that any triple (x,y,z) with x*y*z=2001 has sum in {671, 111, 99}
**Strategy**: Use direct computational verification with omega/linarith tactics and simple case analysis
**Status**: [DEAD_END]
**Failure Reason**: Complex case analysis with by_cases leads to compilation errors and overly verbose proof structure. The nested case analysis becomes unmanageable.

### SUBGOAL_006_ALT2 [DEAD_END]
**Parent Node**: SUBGOAL_003
**Goal**: Complete the sorry in optimization_2001 lemma - prove that any triple (x,y,z) with x*y*z=2001 has sum in {671, 111, 99}
**Strategy**: Use a minimal approach that directly leverages the computational facts without complex case analysis
**Status**: [DEAD_END]
**Failure Reason**: After 6 attempts to complete the computational verification, the proof requires a more sophisticated approach involving systematic enumeration of all divisors and factorizations. The current approach reaches the limit of simple computational tactics and requires deeper mathematical reasoning about unique factorization and divisor properties.
**Progress Made**: File compiles successfully with clean structure. Only one sorry remains, representing the core mathematical challenge of proving exhaustiveness of the three factorizations.
