import Mathlib.Data.Real.Basic
import Mathlib.Data.Real.Pi.Bounds
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.Ring
import Mathlib.Analysis.SpecialFunctions.Trigonometric.Basic

-- Main theorem: For real numbers a, b with a² + b² = 1, prove ab + (a - b) ≤ 1
theorem algebra_sqineq_unitcircatbpamblt1 (a b : ℝ) (h : a^2 + b^2 = 1) : a * b + (a - b) ≤ 1 := by
  -- Strategy 1: Algebraic approach - convert to (1 - a)(1 + b) ≥ 0

  -- Step 1: Prove algebraic equivalence ab + (a - b) ≤ 1 ⇔ (1 - a)(1 + b) ≥ 0
  have algebraic_equiv : a * b + (a - b) ≤ 1 ↔ (1 - a) * (1 + b) ≥ 0 := by
    constructor
    · -- Forward direction: ab + (a - b) ≤ 1 → (1 - a)(1 + b) ≥ 0
      intro h_ineq
      -- Show 1 - (ab + (a - b)) ≥ 0 ⇔ (1 - a)(1 + b) ≥ 0
      have h1 : 1 - (a * b + (a - b)) = 1 - a * b - a + b := by ring
      have h2 : 1 - a * b - a + b = (1 - a) * (1 + b) := by ring
      rw [← h2, ← h1]
      linarith [h_ineq]
    · -- Reverse direction: (1 - a)(1 + b) ≥ 0 → ab + (a - b) ≤ 1
      intro h_prod
      -- Show (1 - a)(1 + b) ≥ 0 → 1 - (ab + (a - b)) ≥ 0
      have h1 : (1 - a) * (1 + b) = 1 + b - a - a * b := by ring
      have h2 : 1 + b - a - a * b = 1 - (a * b + (a - b)) := by ring
      rw [h1, h2] at h_prod
      linarith [h_prod]

  -- Step 2: Prove bounds from unit circle: -1 ≤ a ≤ 1 and -1 ≤ b ≤ 1
  have bounds_a : -1 ≤ a ∧ a ≤ 1 := by
    -- From a² + b² = 1, we have a² ≤ 1, so |a| ≤ 1, hence -1 ≤ a ≤ 1
    have h1 : a^2 ≤ 1 := by
      rw [← h]
      exact le_add_of_nonneg_right (sq_nonneg b)
    have h2 : |a| ≤ 1 := by
      rwa [← sq_le_one_iff_abs_le_one]
    exact abs_le.mp h2

  have bounds_b : -1 ≤ b ∧ b ≤ 1 := by
    -- From a² + b² = 1, we have b² ≤ 1, so |b| ≤ 1, hence -1 ≤ b ≤ 1
    have h1 : b^2 ≤ 1 := by
      rw [← h]
      exact le_add_of_nonneg_left (sq_nonneg a)
    have h2 : |b| ≤ 1 := by
      rwa [← sq_le_one_iff_abs_le_one]
    exact abs_le.mp h2

  -- Step 3: Prove non-negativity of factors
  have nonneg_1_minus_a : 1 - a ≥ 0 := by
    -- From bounds_a.2: a ≤ 1, we get 1 - a ≥ 0
    linarith [bounds_a.2]

  have nonneg_1_plus_b : 1 + b ≥ 0 := by
    -- From bounds_b.1: -1 ≤ b, we get 1 + b ≥ 0
    linarith [bounds_b.1]

  -- Step 4: Conclude (1 - a)(1 + b) ≥ 0
  have product_nonneg : (1 - a) * (1 + b) ≥ 0 := by
    -- Product of two non-negative numbers is non-negative
    exact mul_nonneg nonneg_1_minus_a nonneg_1_plus_b

  -- Apply the equivalence
  rw [algebraic_equiv]
  exact product_nonneg

-- Helper lemma: Algebraic equivalence
lemma algebraic_equivalence (a b : ℝ) :
  a * b + (a - b) ≤ 1 ↔ (1 - a) * (1 + b) ≥ 0 := by
  constructor
  · -- Forward direction: ab + (a - b) ≤ 1 → (1 - a)(1 + b) ≥ 0
    intro h_ineq
    -- Show 1 - (ab + (a - b)) ≥ 0 ⇔ (1 - a)(1 + b) ≥ 0
    have h1 : 1 - (a * b + (a - b)) = 1 - a * b - a + b := by ring
    have h2 : 1 - a * b - a + b = (1 - a) * (1 + b) := by ring
    rw [← h2, ← h1]
    linarith [h_ineq]
  · -- Reverse direction: (1 - a)(1 + b) ≥ 0 → ab + (a - b) ≤ 1
    intro h_prod
    -- Show (1 - a)(1 + b) ≥ 0 → 1 - (ab + (a - b)) ≥ 0
    have h1 : (1 - a) * (1 + b) = 1 + b - a - a * b := by ring
    have h2 : 1 + b - a - a * b = 1 - (a * b + (a - b)) := by ring
    rw [h1, h2] at h_prod
    linarith [h_prod]

-- Helper lemma: Unit circle bounds for a
lemma unit_circle_bounds_a (a b : ℝ) (h : a^2 + b^2 = 1) : -1 ≤ a ∧ a ≤ 1 := by
  -- From a² + b² = 1, we have a² ≤ 1, so |a| ≤ 1, hence -1 ≤ a ≤ 1
  have h1 : a^2 ≤ 1 := by
    rw [← h]
    exact le_add_of_nonneg_right (sq_nonneg b)
  have h2 : |a| ≤ 1 := by
    rwa [← sq_le_one_iff_abs_le_one]
  exact abs_le.mp h2

-- Helper lemma: Unit circle bounds for b
lemma unit_circle_bounds_b (a b : ℝ) (h : a^2 + b^2 = 1) : -1 ≤ b ∧ b ≤ 1 := by
  -- From a² + b² = 1, we have b² ≤ 1, so |b| ≤ 1, hence -1 ≤ b ≤ 1
  have h1 : b^2 ≤ 1 := by
    rw [← h]
    exact le_add_of_nonneg_left (sq_nonneg a)
  have h2 : |b| ≤ 1 := by
    rwa [← sq_le_one_iff_abs_le_one]
  exact abs_le.mp h2

-- Alternative trigonometric proof (simplified)
theorem algebra_sqineq_unitcircatbpamblt1_trig (a b : ℝ) (h : a^2 + b^2 = 1) :
  a * b + (a - b) ≤ 1 := by
  -- Since the algebraic proof is complete and works for all points on the unit circle,
  -- we can simply apply it directly
  exact algebra_sqineq_unitcircatbpamblt1 a b h
