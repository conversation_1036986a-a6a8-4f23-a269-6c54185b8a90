# AMC12 2000 P6 Proof Tree

## Problem Statement
Pick two distinct primes from {5, 7, 11, 13, 17}; which listed number can equal (product) – (sum)?

## ROOT Node
**ID**: ROOT_001
**Status**: [ROOT]
**Goal**: Prove that among the given options, only 119 can equal pq - (p+q) for distinct primes p,q ∈ {5, 7, 11, 13, 17}

## STRATEGY Nodes

### Strategy 1: Algebraic Transformation
**ID**: STRATEGY_001
**Parent Node**: ROOT_001
**Status**: [STRATEGY]
**Detailed Plan**: Transform pq - (p+q) = (p-1)(q-1) - 1 and use properties of odd primes
**Strategy**: Algebraic manipulation and modular arithmetic

### Strategy 2: Congruence Analysis
**ID**: STRATEGY_002
**Parent Node**: STRATEGY_001
**Status**: [STRATEGY]
**Detailed Plan**: Use the fact that for odd primes p,q: (p-1)(q-1) ≡ 0 (mod 4), so pq-(p+q) ≡ 3 (mod 4)
**Strategy**: Modular arithmetic filtering

### Strategy 3: Upper Bound Analysis
**ID**: STRATEGY_003
**Parent Node**: STRATEGY_001
**Status**: [STRATEGY]
**Detailed Plan**: Find maximum possible value using largest primes p=13, q=17
**Strategy**: Extremal analysis

## SUBGOAL Nodes

### Subgoal 1: Algebraic Identity
**ID**: SUBGOAL_001
**Parent Node**: STRATEGY_001
**Status**: [DEAD_END]
**Goal**: Prove pq - (p+q) = (p-1)(q-1) - 1
**Strategy**: Direct algebraic expansion using ring tactics
**Failure Reason**: Natural number subtraction complications prevent ring tactic from working directly

### Subgoal 2: Modular Congruence
**ID**: SUBGOAL_002
**Parent Node**: STRATEGY_002
**Status**: [DEAD_END]
**Goal**: Prove that for odd primes p,q: pq - (p+q) ≡ 3 (mod 4)
**Strategy**: Use properties of even numbers (p-1) and (q-1)
**Failure Reason**: Complex finite set case analysis and modular arithmetic combination proves difficult to automate

### Subgoal 3: Option Filtering
**ID**: SUBGOAL_003
**Parent Node**: SUBGOAL_002
**Status**: [PROVEN]
**Goal**: Check which options satisfy ≡ 3 (mod 4): eliminate 22, 60, 180; keep 119, 231
**Strategy**: Direct modular computation
**Proof Completion**: Successfully proven using simp [Nat.ModEq] tactic

### Subgoal 4: Upper Bound
**ID**: SUBGOAL_004
**Parent Node**: STRATEGY_003
**Status**: [DEAD_END]
**Goal**: Prove maximum value is (13-1)(17-1)-1 = 191, eliminating 231
**Strategy**: Compute maximum with largest available primes
**Failure Reason**: Complex case analysis required for finite set membership, too many subcases to handle efficiently

### Subgoal 5: Constructive Verification
**ID**: SUBGOAL_005
**Parent Node**: ROOT_001
**Status**: [TO_EXPLORE]
**Goal**: Verify 119 = 11×13 - (11+13) = 143 - 24
**Strategy**: Direct computation with specific primes

### Subgoal 6: Final Conclusion
**ID**: SUBGOAL_006
**Parent Node**: ROOT_001
**Status**: [TO_EXPLORE]
**Goal**: Conclude that 119 is the unique answer
**Strategy**: Combine all previous results

### Subgoal 1 Alternative: Direct Computation
**ID**: SUBGOAL_001_ALT
**Parent Node**: STRATEGY_001
**Status**: [PROVEN]
**Goal**: Prove achievable_119 directly by computation
**Strategy**: Use norm_num or direct computation to verify 11*13 - (11+13) = 119
**Proof Completion**: Successfully proven using unfold and norm_num tactics

### Subgoal 7: Simplified Main Theorem
**ID**: SUBGOAL_007
**Parent Node**: ROOT_001
**Status**: [DEAD_END]
**Goal**: Prove main theorem using only proven lemmas
**Strategy**: Use achievable_119 and options_mod_4 to show 119 is the unique answer
**Failure Reason**: Forward direction requires complex finite set case analysis that repeatedly fails compilation after multiple fix attempts
