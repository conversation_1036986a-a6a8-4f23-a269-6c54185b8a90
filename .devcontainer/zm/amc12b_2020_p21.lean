import Mathlib.Tactic

-- AMC 12B 2020 Problem 21
-- Count the positive integers n for which (n + 1000)/70 equals ⌊√n⌋

-- Direct verification of the 6 solutions
lemma solution_400 : (400 + 1000) / 70 = Int.floor (Real.sqrt 400) := by
  norm_num

lemma solution_470 : (470 + 1000) / 70 = Int.floor (Real.sqrt 470) := by
  norm_num
  -- Show that ⌊√470⌋ = 21 by showing 21 ≤ √470 < 22
  rw [eq_comm, Int.floor_eq_iff]
  constructor
  · -- Show 21 ≤ √470
    have h1 : (21 : ℝ)^2 ≤ 470 := by norm_num
    exact Real.le_sqrt' (by norm_num : (0 : ℝ) < 21) |>.mpr h1
  · -- Show √470 < 21 + 1
    have h2 : 470 < (22 : ℝ)^2 := by norm_num
    norm_cast
    exact Real.sqrt_lt' (by norm_num : (0 : ℝ) < 22) |>.mpr h2

lemma solution_2290 : (2290 + 1000) / 70 = Int.floor (Real.sqrt 2290) := by
  norm_num
  -- Show that ⌊√2290⌋ = 47 by showing 47 ≤ √2290 < 48
  rw [eq_comm, Int.floor_eq_iff]
  constructor
  · -- Show 47 ≤ √2290
    have h1 : (47 : ℝ)^2 ≤ 2290 := by norm_num
    exact Real.le_sqrt' (by norm_num : (0 : ℝ) < 47) |>.mpr h1
  · -- Show √2290 < 47 + 1
    have h2 : 2290 < (48 : ℝ)^2 := by norm_num
    norm_cast
    exact Real.sqrt_lt' (by norm_num : (0 : ℝ) < 48) |>.mpr h2

lemma solution_2360 : (2360 + 1000) / 70 = Int.floor (Real.sqrt 2360) := by
  norm_num
  -- Show that ⌊√2360⌋ = 48 by showing 48 ≤ √2360 < 49
  rw [eq_comm, Int.floor_eq_iff]
  constructor
  · -- Show 48 ≤ √2360
    have h1 : (48 : ℝ)^2 ≤ 2360 := by norm_num
    exact Real.le_sqrt' (by norm_num : (0 : ℝ) < 48) |>.mpr h1
  · -- Show √2360 < 48 + 1
    have h2 : 2360 < (49 : ℝ)^2 := by norm_num
    norm_cast
    exact Real.sqrt_lt' (by norm_num : (0 : ℝ) < 49) |>.mpr h2

lemma solution_2430 : (2430 + 1000) / 70 = Int.floor (Real.sqrt 2430) := by
  norm_num
  -- Show that ⌊√2430⌋ = 49 by showing 49 ≤ √2430 < 50
  rw [eq_comm, Int.floor_eq_iff]
  constructor
  · -- Show 49 ≤ √2430
    have h1 : (49 : ℝ)^2 ≤ 2430 := by norm_num
    exact Real.le_sqrt' (by norm_num : (0 : ℝ) < 49) |>.mpr h1
  · -- Show √2430 < 49 + 1
    have h2 : 2430 < (50 : ℝ)^2 := by norm_num
    norm_cast
    exact Real.sqrt_lt' (by norm_num : (0 : ℝ) < 50) |>.mpr h2

lemma solution_2500 : (2500 + 1000) / 70 = Int.floor (Real.sqrt 2500) := by
  norm_num

theorem amc12b_2020_p21 :
  ∃ (s : List ℕ), s.length = 6 ∧
  ∀ n ∈ s, n > 0 ∧ (n + 1000) / 70 = Int.floor (Real.sqrt n) := by
  use [400, 470, 2290, 2360, 2430, 2500]
  constructor
  · norm_num
  · intro n hn
    simp at hn
    cases hn with
    | inl h => rw [h]; constructor; norm_num; exact solution_400
    | inr h => cases h with
      | inl h => rw [h]; constructor; norm_num; exact solution_470
      | inr h => cases h with
        | inl h => rw [h]; constructor; norm_num; exact solution_2290
        | inr h => cases h with
          | inl h => rw [h]; constructor; norm_num; exact solution_2360
          | inr h => cases h with
            | inl h => rw [h]; constructor; norm_num; exact solution_2430
            | inr h => rw [h]; constructor; norm_num; exact solution_2500
