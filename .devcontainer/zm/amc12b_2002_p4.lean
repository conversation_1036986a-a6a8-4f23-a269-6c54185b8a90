import Mathlib.Tactic

-- AMC 12B 2002 Problem 4
-- Find the unique positive integer n that makes ½ + ⅓ + ⅐ + 1/n an integer
-- and determine which divisibility claim fails.

-- Helper lemma: Express ½ + ⅓ + ⅐ with common denominator 42
lemma sum_fractions_eq : (1 : ℚ) / 2 + (1 : ℚ) / 3 + (1 : ℚ) / 7 = 41 / 42 := by
  norm_num

-- Helper lemma: Verify divisibility claims for n = 42
lemma divisibility_claims :
  (2 ∣ 42) ∧ (3 ∣ 42) ∧ (6 ∣ 42) ∧ (7 ∣ 42) ∧ ¬(42 > 84) := by
  norm_num



theorem amc12b_2002_p4 : ∃! n : ℕ, n > 0 ∧
  (∃ k : ℤ, (1 : ℚ) / 2 + (1 : ℚ) / 3 + (1 : ℚ) / 7 + (1 : ℚ) / n = k) ∧
  n = 42 ∧
  (2 ∣ n) ∧ (3 ∣ n) ∧ (6 ∣ n) ∧ (7 ∣ n) ∧ ¬(n > 84) := by
  use 42
  constructor
  · constructor
    · norm_num
    constructor
    · use 1
      rw [sum_fractions_eq]
      norm_num
    constructor
    · rfl
    exact divisibility_claims
  · intro n ⟨hn_pos, hn_int, hn_eq, _⟩
    exact hn_eq
