-- AMC 12B 2002 Problem 2
-- Find the value of (3x − 2)(4x + 1) − (3x − 2)·4x + 1 when x = 4

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic

-- Define the expression as a function
def expr (x : ℝ) : ℝ := (3*x - 2)*(4*x + 1) - (3*x - 2)*4*x + 1

-- Main theorem: the expression equals 11 when x = 4
theorem amc12b_2002_p2 : expr 4 = 11 := by
  -- SUBGOAL_001: Expand definition and use ring to simplify directly
  unfold expr
  -- Apply ring tactic to handle all algebraic manipulation
  ring
