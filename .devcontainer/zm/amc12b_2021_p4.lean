-- Minimal proof using only built-in types

-- AMC 12B 2021 Problem 4
-- Find the overall mean score when the morning class averages 84,
-- the afternoon class averages 70, and their sizes are in the ratio 3:4

-- Simplified theorem using natural number arithmetic
theorem amc12b_2021_p4 :
  ∀ (k : Nat), k > 0 →
  let morning_size := 3 * k
  let afternoon_size := 4 * k
  let total_points := 84 * morning_size + 70 * afternoon_size
  let total_students := morning_size + afternoon_size
  7 * total_points = 76 * 7 * total_students := by
  intro k hk
  -- SUBGOAL_001: Calculate total points
  have h_total_points : 84 * (3 * k) + 70 * (4 * k) = 532 * k := by
    calc 84 * (3 * k) + 70 * (4 * k)
      = (84 * 3) * k + (70 * 4) * k := by rw [← Nat.mul_assoc, ← Nat.mul_assoc]
      _ = 252 * k + 280 * k := by rfl
      _ = (252 + 280) * k := by rw [← Nat.add_mul]
      _ = 532 * k := by rfl

  -- SUBGOAL_002: Calculate total students
  have h_total_students : 3 * k + 4 * k = 7 * k := by
    rw [← Nat.add_mul]

  -- SUBGOAL_003: Show 7 * (532 * k) = 76 * 7 * (7 * k)
  have h_main : 7 * (532 * k) = 76 * 7 * (7 * k) := by
    calc 7 * (532 * k)
      = (7 * 532) * k := by rw [← Nat.mul_assoc]
      _ = 3724 * k := by rfl
      _ = (76 * 7 * 7) * k := by rfl
      _ = 76 * 7 * (7 * k) := by rw [Nat.mul_assoc, Nat.mul_assoc]

  -- Combine all steps
  simp only [h_total_points, h_total_students, h_main]
