# AMC 12B 2021 Problem 1 - Proof Tree

## Problem Statement
Count the integers x satisfying |x| < 3π.

## Proof Tree Structure

### NODE_ROOT [ROOT]
**Goal**: Prove that there are exactly 19 integers x satisfying |x| < 3π
**Status**: [ROOT]
**Children**: STRATEGY_FLOOR_FUNCTION

---

### NODE_STRATEGY_FLOOR_FUNCTION [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Use floor function to convert the inequality |x| < 3π to integer bounds
**Strategy**: Transform |x| < 3π to -3π < x < 3π, then use ⌊3π⌋ to get integer bounds
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_CALCULATE_FLOOR, SUBGOAL_INTEGER_BOUNDS, SUBGOAL_COUNT_INTEGERS

---

### NODE_SUBGOAL_CALCULATE_FLOOR [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FLOOR_FUNCTION
**Goal**: Calculate ⌊3π⌋ = 9
**Expected Result**: Prove that ⌊3π⌋ = 9 using the fact that 3π ≈ 9.424...
**Status**: [DEAD_END]
**Failure Reason**: Complex pi bounds calculations and floor function interactions causing compilation errors. Multiple attempts with different tactics failed due to type mismatches and unsolved goals.

---

### NODE_SUBGOAL_INTEGER_BOUNDS [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FLOOR_FUNCTION
**Goal**: Transform |x| < 3π to integer bounds -9 ≤ x ≤ 9
**Expected Result**: Show that for integers x, |x| < 3π is equivalent to -9 ≤ x ≤ 9
**Status**: [TO_EXPLORE]

---

### NODE_SUBGOAL_COUNT_INTEGERS [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FLOOR_FUNCTION
**Goal**: Count integers from -9 to 9 inclusive
**Expected Result**: Show that there are 19 integers in the range [-9, 9]
**Status**: [TO_EXPLORE]

---

### NODE_STRATEGY_DIRECT_BOUNDS [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Use direct numerical bounds approach without floor function
**Strategy**: Directly prove that for integers x, |x| < 3π ↔ -9 ≤ x ≤ 9 using pi bounds
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_DIRECT_BOUNDS_ALT

---

### NODE_SUBGOAL_DIRECT_BOUNDS_ALT [SUBGOAL]
**Parent Node**: NODE_STRATEGY_DIRECT_BOUNDS
**Goal**: Prove directly that |x| < 3π ↔ -9 ≤ x ≤ 9 for integers x
**Expected Result**: Use pi_gt_three and pi_lt_four to establish bounds without floor function
**Status**: [DEAD_END]
**Failure Reason**: Type mismatches between integer and real absolute values, calc expression type errors, and unknown constants. Multiple attempts with different tactics failed due to complex type coercions.

---

### NODE_STRATEGY_SIMPLE_COUNT [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Use simple counting approach with explicit enumeration
**Strategy**: Simply state that the integers satisfying |x| < 3π are exactly -9, -8, ..., 8, 9 and count them
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_SIMPLE_COUNT_ALT

---

### NODE_SUBGOAL_SIMPLE_COUNT_ALT [SUBGOAL]
**Parent Node**: NODE_STRATEGY_SIMPLE_COUNT
**Goal**: Prove that there are exactly 19 integers from -9 to 9 inclusive
**Expected Result**: Use basic arithmetic to show 9 - (-9) + 1 = 19
**Status**: [DEAD_END]
**Failure Reason**: Compilation continues to fail with unsolved goals and type mismatches. After 8 attempts with different approaches, automatic fixes are ineffective.

---

### NODE_STRATEGY_FIX_COMPILATION [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Fix specific compilation errors in current code by addressing unsolved goals
**Strategy**: Add missing imports, fix type coercions, and complete the proof steps that have unsolved goals
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_FIX_LINARITH, SUBGOAL_FIX_ABS_LE, SUBGOAL_FIX_CAST_TYPES

---

### NODE_SUBGOAL_FIX_LINARITH [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FIX_COMPILATION
**Goal**: Fix the linarith tactic error and unsolved False goal
**Expected Result**: Add proper imports and fix the contradiction proof
**Status**: [DEAD_END]
**Failure Reason**: Multiple attempts to fix linarith and contradiction proofs failed. The complex proof structure with by_contra and type coercions is causing persistent unsolved goals.

---

### NODE_SUBGOAL_FIX_ABS_LE [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FIX_COMPILATION
**Goal**: Fix the abs_le proof that has unsolved goals
**Expected Result**: Complete the proof that |x| ≤ 9 implies -9 ≤ x ∧ x ≤ 9
**Status**: [DEAD_END]
**Failure Reason**: Complex arithmetic calculations and type mismatches in pi bounds. Multiple attempts to fix calc expressions failed due to incorrect numerical bounds and type coercion issues.

---

### NODE_SUBGOAL_FIX_CAST_TYPES [SUBGOAL]
**Parent Node**: NODE_STRATEGY_FIX_COMPILATION
**Goal**: Fix type casting issues between Int and Real
**Expected Result**: Properly handle coercions between integer and real absolute values
**Status**: [PROVEN]
**Proof Completion**: Successfully fixed type casting issues using proper coercion syntax ↑|x| and Int.cast_le.mpr, file now compiles with only sorry warnings

---

### NODE_STRATEGY_MINIMAL_PROOF [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Use minimal proof structure with basic tactics only
**Strategy**: Rewrite the theorem with simpler structure avoiding complex type coercions and by_contra
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_MINIMAL_REWRITE

---

### NODE_SUBGOAL_MINIMAL_REWRITE [SUBGOAL]
**Parent Node**: NODE_STRATEGY_MINIMAL_PROOF
**Goal**: Rewrite the entire proof using only basic tactics like simp, exact, and constructor
**Expected Result**: Complete proof without complex calc expressions or by_contra
**Status**: [DEAD_END]
**Failure Reason**: Persistent unsolved goals with type casting and integer/real coercions. Multiple simplification attempts failed due to complex Mathlib type system interactions.

---

### NODE_STRATEGY_COMPLETE_SORRY [STRATEGY]
**Parent Node**: NODE_ROOT
**Detailed Plan**: Complete the remaining sorry statements in the working proof framework
**Strategy**: Replace each sorry with appropriate tactics now that type issues are resolved
**Status**: [TO_EXPLORE]
**Children**: SUBGOAL_COMPLETE_PI_BOUND, SUBGOAL_COMPLETE_INT_BOUND, SUBGOAL_COMPLETE_ABS_LE

---

### NODE_SUBGOAL_COMPLETE_PI_BOUND [SUBGOAL]
**Parent Node**: NODE_STRATEGY_COMPLETE_SORRY
**Goal**: Complete the proof that 3π < 10
**Expected Result**: Use pi bounds to show 3π < 10
**Status**: [PROMISING]
**Tactical Details**: Use Real.pi_lt_four and arithmetic to show 3π < 3*4 = 12, but need tighter bound. Use Real.pi < 3.2 to get 3π < 9.6 < 10

---

### NODE_SUBGOAL_COMPLETE_INT_BOUND [SUBGOAL]
**Parent Node**: NODE_STRATEGY_COMPLETE_SORRY
**Goal**: Complete the proof that for integers, |x| < 10 implies |x| ≤ 9
**Expected Result**: Use integer properties to show the bound
**Status**: [PROMISING]
**Tactical Details**: Use Int.natAbs_lt and integer properties to show that for integers, |x| < 10 means |x| ≤ 9

---

### NODE_SUBGOAL_COMPLETE_ABS_LE [SUBGOAL]
**Parent Node**: NODE_STRATEGY_COMPLETE_SORRY
**Goal**: Complete the proof that |x| ≤ 9 implies -9 ≤ x ∧ x ≤ 9
**Expected Result**: Use abs_le lemma properly
**Status**: [PROMISING]
**Tactical Details**: Use abs_le.mp to convert |x| ≤ 9 to -9 ≤ x ∧ x ≤ 9 directly

---

## Current Status Summary
- **Active Nodes**: NODE_STRATEGY_COMPLETE_SORRY and its children are [TO_EXPLORE]
- **Proven Nodes**: NODE_SUBGOAL_FIX_CAST_TYPES is [PROVEN]
- **Dead End Nodes**: Previous strategies are [DEAD_END] but compilation is now working
- **Next Action**: Begin tactical execution starting with NODE_SUBGOAL_COMPLETE_PI_BOUND
