# AMC 12B 2020 Problem 2 - Proof Tree

## Problem Statement
Evaluate (100² − 7²)/(70² − 11²) · (70−11)(70+11)/(100−7)(100+7).

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the given expression equals 1
**Strategy**: Use difference of squares factorization and algebraic cancellation

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Factor 100² − 7² using difference of squares: (100−7)(100+7)
2. Factor 70² − 11² using difference of squares: (70−11)(70+11)
3. Substitute factorizations into the expression
4. Cancel common factors to show the result equals 1

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Factor 100² − 7² as (100−7)(100+7)
**Strategy**: Apply difference of squares formula: a² − b² = (a−b)(a+b)
**Mathlib Reference**: sq_sub_sq, ring
**Proof Completion**: Used sq_sub_sq and ring to prove the factorization

### SUBGOAL_002 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Factor 70² − 11² as (70−11)(70+11)
**Strategy**: Apply difference of squares formula: a² − b² = (a−b)(a+b)
**Mathlib Reference**: sq_sub_sq, ring
**Proof Completion**: Used sq_sub_sq and ring to prove the factorization

### SUBGOAL_003 [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Substitute factorizations into the original expression
**Strategy**: Replace each difference of squares with its factored form
**Mathlib Reference**: rw, simp
**Failure Reason**: Rewrite tactics consistently fail to find patterns in the expression due to type casting issues and complex expression structure

### SUBGOAL_003_ALT [PROMISING]
**Parent Node**: STRATEGY_001
**Goal**: Use direct numerical computation to prove the equality
**Strategy**: Compute the exact numerical values and use norm_num to verify equality
**Mathlib Reference**: norm_num, simp_all
**Concrete Tactics**:
```lean
simp_all only [pow_two]
norm_num
```

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Show that all factors cancel to give 1
**Strategy**: Demonstrate that (a·b)/(c·d) · (c·d)/(a·b) = 1
**Mathlib Reference**: mul_div_cancel, div_mul_cancel

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Verify the final result equals 1
**Strategy**: Use algebraic simplification and cancellation properties
**Mathlib Reference**: one_mul, mul_one, div_self

## Current Status
- Phase 1: Proof tree initialization complete
- Next: Generate code framework with sorry placeholders
