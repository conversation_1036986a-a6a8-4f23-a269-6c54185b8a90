-- AMC 12B 2002 Problem 19
-- Find the value of abc given positive reals a(b+c)=152, b(c+a)=162, c(a+b)=170

import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Field.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith

theorem amc12b_2002_p19 (a b c : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c)
  (h1 : a * (b + c) = 152)
  (h2 : b * (c + a) = 162)
  (h3 : c * (a + b) = 170) :
  a * b * c = 720 := by
  -- Setup substitution: let x = ab, y = bc, z = ca
  let x := a * b
  let y := b * c
  let z := c * a

  -- Transform equations using substitution
  have eq1 : x + z = 152 := by
    -- a * (b + c) = a * b + a * c = x + z
    rw [← h1]
    ring
  have eq2 : x + y = 162 := by
    -- b * (c + a) = b * c + b * a = y + x
    rw [← h2]
    ring
  have eq3 : y + z = 170 := by
    -- c * (a + b) = c * a + c * b = z + y
    rw [← h3]
    ring

  -- Solve linear system
  -- From eq1: x + z = 152, eq2: x + y = 162, eq3: y + z = 170
  -- Adding all three: 2(x + y + z) = 484, so x + y + z = 242
  have sum_xyz : x + y + z = 242 := by linarith [eq1, eq2, eq3]

  have hx : x = 72 := by
    -- x = (x + y + z) - (y + z) = 242 - 170 = 72
    linarith [sum_xyz, eq3]

  have hy : y = 90 := by
    -- y = (x + y + z) - (x + z) = 242 - 152 = 90
    linarith [sum_xyz, eq1]

  have hz : z = 80 := by
    -- z = (x + y + z) - (x + y) = 242 - 162 = 80
    linarith [sum_xyz, eq2]

  -- Calculate abc from ab, bc, ca
  have h_abc_sq : (a * b * c) ^ 2 = x * y * z := by
    -- (abc)² = (ab)(bc)(ca) = a²b²c² = (abc)²
    -- So we need to show (abc)² = xyz
    have h_expand : x * y * z = (a * b) * (b * c) * (c * a) := by
      simp only [x, y, z]
    rw [h_expand]
    ring

  have h_xyz : x * y * z = 518400 := by
    rw [hx, hy, hz]
    norm_num

  have h_abc : a * b * c = 720 := by
    -- Since (abc)² = xyz = 518400 and abc > 0, we have abc = 720
    have h_pos : 0 < a * b * c := by
      apply mul_pos
      apply mul_pos
      exact ha
      exact hb
      exact hc
    have h_sq : (a * b * c) ^ 2 = 518400 := by
      rw [h_abc_sq, h_xyz]
    -- We know that 720² = 518400, so we can solve directly
    have h_check : (720 : ℝ) ^ 2 = 518400 := by norm_num
    have h_eq : (a * b * c) ^ 2 = (720 : ℝ) ^ 2 := by
      rw [h_sq, ← h_check]
    -- Since both abc and 720 are positive, and their squares are equal, they must be equal
    have h_cases : a * b * c = 720 ∨ a * b * c = -720 := sq_eq_sq_iff_eq_or_eq_neg.mp h_eq
    cases h_cases with
    | inl h => exact h
    | inr h =>
      -- This case is impossible since abc > 0 but -720 < 0
      have h_neg : a * b * c < 0 := by rw [h]; norm_num
      exact absurd h_neg (not_lt.mpr (le_of_lt h_pos))

  exact h_abc
