# AMC 12A 2009 Problem 6 - Proof Tree

## Problem Statement
Determine which listed expression always equals 12^(mn) when P = 2^m and Q = 3^n.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that among the given options, only P^(2n)Q^m equals 12^(mn) for all m,n
**Strategy**: Use prime factorization to rewrite expressions and match exponents

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Express 12^(mn) in prime factorization: 12^(mn) = (2^2 · 3)^(mn) = 2^(2mn) · 3^(mn)
2. Substitute P = 2^m and Q = 3^n into each candidate expression
3. Rewrite each candidate in prime factorization form 2^(a) · 3^(b)
4. Compare exponents with target 2^(2mn) · 3^(mn)
5. Verify that only option (E) P^(2n)Q^m matches exactly

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Express target 12^(mn) in prime factorization
**Strategy**: Use 12 = 2^2 · 3 and exponent rules
**Mathlib Reference**: mul_pow, pow_mul, norm_num, ring_nf
**Proof Completion**: Used calc proof with norm_num, mul_pow, and pow_mul to show 12^(mn) = 2^(2mn) * 3^(mn)

### SUBGOAL_002 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Analyze option (A): P^2 · Q = 2^(2m) · 3^n
**Strategy**: Substitute P = 2^m, Q = 3^n and show exponents don't match target
**Mathlib Reference**: Nat.pow_mul

### SUBGOAL_003 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Analyze option (B): P^n · Q^m = 2^(mn) · 3^(mn)
**Strategy**: Substitute P = 2^m, Q = 3^n and show exponents don't match target
**Mathlib Reference**: Nat.pow_mul

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Analyze option (C): P^n · Q^(2m) = 2^(mn) · 3^(2mn)
**Strategy**: Substitute P = 2^m, Q = 3^n and show exponents don't match target
**Mathlib Reference**: Nat.pow_mul

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Analyze option (D): P^(2m) · Q^n = 2^(2m^2) · 3^(n^2)
**Strategy**: Substitute P = 2^m, Q = 3^n and show exponents don't match target
**Mathlib Reference**: Nat.pow_mul

### SUBGOAL_006 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Prove option (E): P^(2n) · Q^m = 2^(2mn) · 3^(mn) = 12^(mn)
**Strategy**: Substitute P = 2^m, Q = 3^n and show exact match with target
**Mathlib Reference**: pow_mul, ring_nf
**Proof Completion**: Used pow_mul to expand exponents and ring_nf to verify equality

### SUBGOAL_007 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Prove uniqueness - only option (E) works for all m,n
**Strategy**: Show counterexamples for options (A)-(D) with specific m,n values
**Mathlib Reference**: Nat.pow_ne_pow_iff

## Current Status
- Phase 1: Proof tree initialization complete
- Next: Generate code framework with sorry placeholders
