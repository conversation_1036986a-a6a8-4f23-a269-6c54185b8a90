import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Ring.Basic
import Mathlib.Tactic

-- AMC 12B 2020 Problem 2
-- Eva<PERSON>ate (100² − 7²)/(70² − 11²) · (70−11)(70+11)/(100−7)(100+7)

-- Let me check the exact computation
#eval (100^2 - 7^2 : ℕ)  -- 9951
#eval (70^2 - 11^2 : ℕ)  -- 4779
#eval ((70 - 11) * (70 + 11) : ℕ)  -- 4779
#eval ((100 - 7) * (100 + 7) : ℕ)  -- 9951

-- Let me check the actual computation step by step
#eval (100^2 - 7^2 : ℕ)  -- 9951
#eval (70^2 - 11^2 : ℕ)   -- 4779
#eval (70 - 11 : ℕ) * (70 + 11)  -- 59 * 81 = 4779
#eval (100 - 7 : ℕ) * (100 + 7)  -- 93 * 107 = 9951

-- The expression is: (9951/4779) * (4779/9951) = 1
-- But let me check if there's a different interpretation
-- Maybe the expression is: (9951/4779) * (4779/9951)
#eval (9951 : ℚ) / 4779 * (4779 / 9951)  -- This should be 1

-- Let me also check the exact expression as written
-- (100² − 7²)/(70² − 11²) · (70−11)(70+11)/(100−7)(100+7)
#eval ((100^2 - 7^2 : ℚ) / (70^2 - 11^2)) * (((70 - 11) * (70 + 11)) / ((100 - 7) * (100 + 7)))

theorem amc12b_2020_p2 :
  ((100 : ℝ)^2 - 7^2) / (70^2 - 11^2) * ((70 - 11) * (70 + 11)) / ((100 - 7) * (100 + 7)) = 1 := by
  -- Use direct numerical computation with explicit real number types
  norm_num
