# AMC 12B 2021 Problem 4 - Proof Tree

## Problem Statement
Find the overall mean score when the morning class averages 84, the afternoon class averages 70, and their sizes are in the ratio 3:4.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the overall mean score is 76
**Strategy**: Use weighted average formula with class sizes in ratio 3:4

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Let morning class size = 3k and afternoon class size = 4k for some k > 0
2. Calculate total points: 84·3k + 70·4k = 252k + 280k = 532k
3. Calculate total students: 3k + 4k = 7k
4. Overall mean = total points / total students = 532k / 7k = 532/7 = 76

### SUBGOAL_001 [PROMISING]
**Parent Node**: STRATEGY_001
**Goal**: Set up variables for class sizes in ratio 3:4
**Strategy**: Define morning class size as 3k and afternoon class size as 4k
**Mathlib Reference**: Nat.cast, simp
**Concrete Tactics**:
```lean
simp only [Nat.cast_mul]
```

### SUBGOAL_002 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Calculate total points from both classes
**Strategy**: Sum weighted scores: 84·3k + 70·4k = 532k
**Mathlib Reference**: Nat.mul_assoc, Nat.add_mul, calc
**Proof Completion**: Used calc proof with associativity and distributivity to show 84*(3*k) + 70*(4*k) = 532*k

### SUBGOAL_003 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Calculate total number of students
**Strategy**: Sum class sizes: 3k + 4k = 7k
**Mathlib Reference**: Nat.add_mul
**Proof Completion**: Used distributivity of multiplication over addition to show 3*k + 4*k = (3+4)*k = 7*k

### SUBGOAL_004 [PROMISING]
**Parent Node**: STRATEGY_001
**Goal**: Show 7 * (532 * k) = 76 * 7 * (7 * k)
**Strategy**: Use arithmetic: 7 * 532 = 3724 and 76 * 7 * 7 = 3724
**Mathlib Reference**: Nat.mul_assoc, rfl
**Concrete Tactics**:
```lean
rw [Nat.mul_assoc, Nat.mul_assoc]
rw [show 7 * 532 = 3724 by rfl]
rw [show 76 * 7 * 7 = 3724 by rfl]
```

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Simplify 532/7 to get final answer 76
**Strategy**: Use division: 532 = 7 × 76
**Mathlib Reference**: Nat.div_eq, norm_num

### SUBGOAL_006 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Alternative verification using weighted average formula
**Strategy**: μ = (3/7)·84 + (4/7)·70 = 36 + 40 = 76
**Mathlib Reference**: Rat.add, Rat.mul, norm_num

## Current Status
- Phase 1: Proof tree initialization complete
- Next: Generate code framework with sorry placeholders
