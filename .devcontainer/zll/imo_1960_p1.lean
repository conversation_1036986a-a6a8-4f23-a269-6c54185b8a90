/-
IMO 1960 Problem 1

Determine all three-digit numbers N having the property that N is divisible by 11, and
N/11 is equal to the sum of the squares of the digits of N.

Strategy:
1. Define the problem predicate: three-digit number divisible by 11 where N/11 equals sum of squares of digits
2. Define solution predicate: N = 550 or N = 803
3. Prove bounds: 100 ≤ N < 1000 for three-digit numbers
4. Exhaustive search through multiples of 11 in range [99, 1001)
5. Verify each candidate by checking if quotient equals sum of squares of digits
6. Prove equivalence between problem and solution predicates
-/

import Mathlib.Data.Nat.Digits
import Mathlib.Tactic.IntervalCases

open Nat

namespace Imo1960Q1

def sumOfSquares (L : List ℕ) : ℕ :=
  (L.map fun x => x * x).sum

def ProblemPredicate (n : ℕ) : Prop :=
  (Nat.digits 10 n).length = 3 ∧ 11 ∣ n ∧ n / 11 = sumOfSquares (Nat.digits 10 n)

def SolutionPredicate (n : ℕ) : Prop :=
  n = 550 ∨ n = 803

-- Prove that three digit numbers are non-zero
theorem not_zero {n : ℕ} (h1 : ProblemPredicate n) : n ≠ 0 := by
  have h2 : Nat.digits 10 n ≠ [] := by
    intro h_empty
    have h_len : (Nat.digits 10 n).length = 0 := by rw [h_empty]; simp
    have : (Nat.digits 10 n).length = 3 := h1.1
    rw [h_len] at this
    norm_num at this
  exact Nat.digits_ne_nil_iff_ne_zero.mp h2

-- Prove lower bound: three-digit numbers are ≥ 100
theorem ge_100 {n : ℕ} (h1 : ProblemPredicate n) : 100 ≤ n := by
  have h2 : 10 ^ 3 ≤ 10 * n := by
    rw [← h1.left]
    refine Nat.base_pow_length_digits_le 10 n ?_ (not_zero h1)
    norm_num
  omega

-- Prove upper bound: three-digit numbers are < 1000
theorem lt_1000 {n : ℕ} (h1 : ProblemPredicate n) : n < 1000 := by
  have h2 : n < 10 ^ 3 := by
    rw [← h1.left]
    refine Nat.lt_base_pow_length_digits ?_
    norm_num
  omega

-- Define search predicate for exhaustive verification
def SearchUpTo (c n : ℕ) : Prop :=
  n = c * 11 ∧ ∀ m : ℕ, m < n → ProblemPredicate m → SolutionPredicate m

-- Base case: start search from 99 = 9 * 11
theorem searchUpTo_start : SearchUpTo 9 99 := by
  constructor
  · norm_num
  · intro m h p
    exfalso
    have : 100 ≤ m := ge_100 p
    omega

-- Inductive step: extend search by checking next multiple of 11
theorem searchUpTo_step {c n} (H : SearchUpTo c n) {c' n'} (ec : c + 1 = c') (en : n + 11 = n') {l}
    (el : Nat.digits 10 n = l) (H' : c = sumOfSquares l → c = 50 ∨ c = 73) : SearchUpTo c' n' := by
  subst ec; subst en; subst el
  obtain ⟨rfl, H⟩ := H
  refine ⟨by ring, fun m hm pp => ?_⟩
  obtain ⟨h₁, ⟨k, rfl⟩, h₂⟩ := pp
  by_cases h : 11 * k < c * 11
  · exact H _ h ⟨h₁, ⟨k, rfl⟩, h₂⟩
  · obtain rfl : k = c := by omega
    rw [Nat.mul_div_cancel_left _ (by norm_num : 11 > 0), mul_comm] at h₂
    cases H' h₂ with
    | inl h_50 =>
      rw [h_50]
      left
      norm_num
    | inr h_73 =>
      rw [h_73]
      right
      norm_num

-- End case: complete search at 1001
theorem searchUpTo_end {c} (H : SearchUpTo c 1001) {n : ℕ} (ppn : ProblemPredicate n) :
    SolutionPredicate n := by
  have h : n < 1001 := by
    have : n < 1000 := lt_1000 ppn
    omega
  exact H.2 _ h ppn

-- Main direction: every solution satisfies the problem predicate
theorem right_direction {n : ℕ} : ProblemPredicate n → SolutionPredicate n := by
  -- Use the SearchUpTo framework with iterate tactic like the Archive solution
  have := searchUpTo_start
  iterate 82
    replace :=
      searchUpTo_step this (by norm_num; rfl) (by norm_num; rfl) rfl
        (by norm_num <;> decide)
  exact searchUpTo_end this

-- Reverse direction: verify that 550 and 803 satisfy the problem predicate
theorem left_direction (n : ℕ) (spn : SolutionPredicate n) : ProblemPredicate n := by
  cases spn with
  | inl h => -- n = 550
    rw [h]
    simp only [ProblemPredicate, sumOfSquares]
    norm_num
  | inr h => -- n = 803
    rw [h]
    simp only [ProblemPredicate, sumOfSquares]
    norm_num

end Imo1960Q1

open Imo1960Q1

-- Final theorem: equivalence between problem and solution predicates
theorem imo1960_q1 (n : ℕ) : ProblemPredicate n ↔ SolutionPredicate n := by
  constructor
  · exact right_direction
  · exact left_direction n
