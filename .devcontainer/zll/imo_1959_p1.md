# IMO 1959 Problem 1 - Proof Tree

## Problem Statement
Show that 21n + 4 and 14n + 3 share no common divisor larger than 1, i.e. gcd(21n + 4, 14n + 3) = 1 for every n ∈ ℕ.

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove gcd(21n + 4, 14n + 3) = 1 for every n ∈ ℕ
**Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use linear combination approach - find integers a, b such that a(21n + 4) + b(14n + 3) = 1, which forces gcd to be 1
**Strategy**: Linear combination method with <PERSON><PERSON><PERSON><PERSON>'s identity
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Find linear combination 3(14n + 3) - 2(21n + 4) = 1
**Strategy**: Direct computation of the linear combination
**Status**: [PROVEN]
**Proof Completion**: Used `ring` tactic to prove the arithmetic identity

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Show that if d divides both terms, then d divides their linear combination
**Strategy**: Use divisibility properties
**Status**: [DEAD_END]
**Failure Reason**: Multiple compilation failures with divisibility lemmas. Issues with Int.dvd_mul_of_dvd_right not existing, Nat.sub_add_cancel type mismatches, and complex type conversions between Nat and Int gcd. After 6 fix attempts, unable to resolve syntax and type errors.

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Conclude that d must divide 1, therefore d = 1
**Strategy**: Use IsCoprime with linear combination to prove coprimality directly
**Status**: [PROVEN]
**Detailed Plan**: Used the linear combination (-2) * (21n + 4) + 3 * (14n + 3) = 1 to prove IsCoprime, then converted to Nat.Coprime.
**Proof Completion**: Applied IsCoprime definition with witnesses (-2, 3), used ring tactic to verify the linear combination equals 1, then used Nat.isCoprime_iff_coprime to convert to natural number coprimality.

### STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Alternative approach using Euclidean algorithm
**Strategy**: Apply Euclidean algorithm repeatedly: gcd(21n + 4, 14n + 3) = gcd(14n + 3, 7n + 1) = gcd(7n + 1, 1) = 1
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Show gcd(21n + 4, 14n + 3) = gcd(14n + 3, (21n + 4) - (14n + 3))
**Strategy**: Apply Euclidean algorithm step 1
**Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Simplify to gcd(14n + 3, 7n + 1)
**Strategy**: Arithmetic simplification
**Status**: [TO_EXPLORE]

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Show gcd(14n + 3, 7n + 1) = gcd(7n + 1, (14n + 3) - 2(7n + 1))
**Strategy**: Apply Euclidean algorithm step 2
**Status**: [TO_EXPLORE]

### SUBGOAL_007 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Simplify to gcd(7n + 1, 1) = 1
**Strategy**: Final arithmetic and gcd property
**Status**: [TO_EXPLORE]
