import Mathlib.Data.Nat.GCD.Basic
import Mathlib.Data.Int.GCD
import Mathlib.RingTheory.Coprime.Lemmas
import Mathlib.Tactic.Ring

-- IMO 1959 Problem 1: Prove that gcd(21n + 4, 14n + 3) = 1 for every n ∈ ℕ

theorem imo_1959_p1 (n : ℕ) : Nat.gcd (21 * n + 4) (14 * n + 3) = 1 := by
  -- Use the fact that gcd(a, b) = 1 iff they are coprime
  rw [Nat.coprime_iff_gcd_eq_one.symm]
  -- Show that 21n + 4 and 14n + 3 are coprime using linear combination
  -- We have 3*(14n + 3) - 2*(21n + 4) = 1, so they are coprime
  rw [← Nat.isCoprime_iff_coprime]
  -- Show IsCoprime (21 * n + 4 : ℤ) (14 * n + 3)
  use (-2 : ℤ), (3 : ℤ)
  -- Prove (-2) * (21 * n + 4) + 3 * (14 * n + 3) = 1
  -- This expands to: -2(21n + 4) + 3(14n + 3) = -42n - 8 + 42n + 9 = 1
  simp only [Int.natCast_add, Int.natCast_mul]
  ring
