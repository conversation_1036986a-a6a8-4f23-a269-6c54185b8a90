# Proof Tree: Unit Circle Inequality ab + (a - b) ≤ 1

## ROOT_001 [ROOT]
**Theorem Statement**: For real numbers a, b with a² + b² = 1, prove ab + (a - b) ≤ 1
**Parent Node**: None
**Status**: [ROOT]

## STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Use algebraic manipulation to convert the inequality to a product of non-negative factors
**Strategy**: Transform ab + (a - b) ≤ 1 to (1 - a)(1 + b) ≥ 0 and use unit circle constraints
**Status**: [PROVEN]
**Proof Completion**: Successfully completed all subgoals. Main theorem proven using algebraic equivalence, unit circle bounds, non-negativity of factors, and product of non-negative numbers.

### SUBGOAL_001 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove algebraic equivalence: ab + (a - b) ≤ 1 ⇔ (1 - a)(1 + b) ≥ 0
**Strategy**: Direct algebraic manipulation using ring operations
**Status**: [PROVEN]
**Proof Completion**: Used constructor to split biconditional, then ring tactics to expand and rearrange terms, followed by <PERSON><PERSON><PERSON> to handle the linear inequalities. Key insight: 1 - (ab + (a - b)) = (1 - a)(1 + b) through algebraic manipulation.

### SUBGOAL_002 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove bounds from unit circle: -1 ≤ a ≤ 1 and -1 ≤ b ≤ 1 when a² + b² = 1
**Strategy**: Use properties of squares and unit circle constraint
**Status**: [PROVEN]
**Proof Completion**: Used unit circle constraint a² + b² = 1 to derive a² ≤ 1 and b² ≤ 1, then applied sq_le_one_iff_abs_le_one to get |a| ≤ 1 and |b| ≤ 1, finally used abs_le.mp to decompose into -1 ≤ a ≤ 1 and -1 ≤ b ≤ 1.

### SUBGOAL_003 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Prove non-negativity: 1 - a ≥ 0 and 1 + b ≥ 0
**Strategy**: Apply bounds from SUBGOAL_002
**Status**: [PROVEN]
**Proof Completion**: Used linarith with bounds_a.2 (a ≤ 1) to prove 1 - a ≥ 0, and bounds_b.1 (-1 ≤ b) to prove 1 + b ≥ 0.

### SUBGOAL_004 [SUBGOAL]
**Parent Node**: STRATEGY_001
**Goal**: Conclude (1 - a)(1 + b) ≥ 0 from non-negativity of factors
**Strategy**: Use multiplication of non-negative numbers
**Status**: [PROVEN]
**Proof Completion**: Applied mul_nonneg lemma with the non-negativity results from SUBGOAL_003 to prove (1 - a)(1 + b) ≥ 0.

## STRATEGY_002 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**: Alternative trigonometric approach using parametrization a = cos θ, b = sin θ
**Strategy**: Substitute trigonometric values and use trigonometric identities
**Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Parametrize unit circle: a = cos θ, b = sin θ for some θ
**Strategy**: Use trigonometric representation of unit circle
**Status**: [TO_EXPLORE]

### SUBGOAL_006 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Simplify ab + (a - b) = sin θ cos θ + cos θ - sin θ
**Strategy**: Direct substitution and trigonometric simplification
**Status**: [TO_EXPLORE]

### SUBGOAL_007 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Show sin θ cos θ + cos θ - sin θ = ½ sin 2θ + cos θ - sin θ
**Strategy**: Use double angle formula sin 2θ = 2 sin θ cos θ
**Status**: [TO_EXPLORE]

### SUBGOAL_008 [SUBGOAL]
**Parent Node**: STRATEGY_002
**Goal**: Prove ½ sin 2θ + cos θ - sin θ ≤ 1
**Strategy**: Show this equals 1 - (1 - cos θ)(1 + sin θ) ≤ 1
**Status**: [TO_EXPLORE]
