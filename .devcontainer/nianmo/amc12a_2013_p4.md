# AMC 12A 2013 Problem 4 - Proof Tree

## Problem Statement
Evaluate $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}}$

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}} = \frac{5}{3}$
**Parent Node**: None
**Strategy**: Factor out common power and simplify

### STRATEGY_001 [STRATEGY]
**Goal**: Use factorization approach to simplify the expression
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Factor out $2^{2012}$ from both numerator and denominator
2. Simplify the resulting expression with smaller exponents
3. Compute the final rational number

### SUBGOAL_001 [PROVEN]
**Goal**: Factor out $2^{2012}$ from numerator and denominator
**Parent Node**: STRATEGY_001
**Strategy**: Use the identity $2^{2014} = 2^{2012} \cdot 2^2$ to rewrite both terms
**Detailed Plan**:
- Use `pow_add` lemma: $2^{2014} = 2^{2012+2} = 2^{2012} \cdot 2^2$
- Apply `ring` tactic to factor: $2^{2012}(2^2 + 1)$ and $2^{2012}(2^2 - 1)$
**Proof Completion**: Successfully proven using `pow_add` and `ring` tactics
**Mathlib Tactics**: `pow_add`, `ring`

### SUBGOAL_002 [DEAD_END]
**Goal**: Simplify the fraction after factorization
**Parent Node**: STRATEGY_001
**Strategy**: Cancel common factors and compute the result
**Failure Reason**: Division cancellation lemmas are not working properly with the current imports and approach. The `mul_div_mul_left` lemma requires specific conditions that are difficult to satisfy in this context.

### SUBGOAL_002_ALT [PROVEN]
**Goal**: Use direct computation approach
**Parent Node**: STRATEGY_001
**Strategy**: Compute the expression directly using `norm_num` and `ring`
**Detailed Plan**:
- Use `pow_add` to rewrite $2^{2014} = 2^{2012} \cdot 4$
- Substitute and use `ring_nf` to simplify the fraction directly
**Proof Completion**: Successfully proven using `pow_add`, `rw`, and `ring_nf` tactics
**Mathlib Tactics**: `pow_add`, `rw`, `ring_nf`

### SUBGOAL_003 [PROVEN]
**Goal**: Formalize the proof in Lean 4 with proper theorem statement
**Parent Node**: STRATEGY_001
**Strategy**: Create a theorem with explicit computation steps
**Proof Completion**: Successfully completed with clean, working Lean 4 code that compiles without errors
**Final Result**: The theorem `amc12a_2013_p4 : (2^2014 + 2^2012) / (2^2014 - 2^2012) = 5 / 3` is proven
