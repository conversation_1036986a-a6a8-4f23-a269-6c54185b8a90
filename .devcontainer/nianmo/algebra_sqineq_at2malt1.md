# Proof Tree: a(2 - a) ≤ 1 for all real a

## Node Structure

### ROOT_001 [ROOT]
- **Theorem**: For all real numbers a, a(2 - a) ≤ 1
- **Status**: [ROOT]
- **Children**: STRATEGY_001, STRATEGY_002

### STRATEGY_001 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Algebraic approach using perfect square factorization
- **Strategy**: Rewrite 1 - a(2 - a) as (a - 1)² and show it's non-negative
- **Status**: [TO_EXPLORE]
- **Children**: SUBGOAL_001, SUBGOAL_002, SUBGOAL_003

### SUBGOAL_001 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Expand and simplify 1 - a(2 - a)
- **Target**: Show 1 - a(2 - a) = 1 - 2a + a²
- **Status**: [PROVEN]
- **Proof Completion**: Used `ring` tactic for algebraic simplification

### SUBGOAL_002 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Factor the expression 1 - 2a + a²
- **Target**: Show 1 - 2a + a² = (a - 1)²
- **Status**: [PROVEN]
- **Proof Completion**: Used `ring` tactic for algebraic factorization

### SUBGOAL_003 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Prove (a - 1)² ≥ 0 and conclude a(2 - a) ≤ 1
- **Target**: Use non-negativity of squares to establish the inequality
- **Status**: [PROVEN]
- **Proof Completion**: Used `sq_nonneg` theorem for non-negativity and `linarith` for final conclusion

### STRATEGY_002 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Calculus approach using optimization
- **Strategy**: Find maximum of f(a) = a(2 - a) using derivatives
- **Status**: [TO_EXPLORE]
- **Children**: SUBGOAL_004, SUBGOAL_005, SUBGOAL_006

### SUBGOAL_004 [SUBGOAL]
- **Parent Node**: STRATEGY_002
- **Goal**: Define function f(a) = a(2 - a) = -a² + 2a
- **Target**: Set up the optimization problem
- **Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
- **Parent Node**: STRATEGY_002
- **Goal**: Find critical points using f'(a) = -2a + 2 = 0
- **Target**: Show critical point is at a = 1
- **Status**: [TO_EXPLORE]

### SUBGOAL_006 [SUBGOAL]
- **Parent Node**: STRATEGY_002
- **Goal**: Verify maximum using f''(a) = -2 < 0 and evaluate f(1) = 1
- **Target**: Conclude f(a) ≤ 1 for all real a
- **Status**: [TO_EXPLORE]

## Priority Queue
1. STRATEGY_001 (Algebraic approach - simpler for Lean 4)
2. STRATEGY_002 (Calculus approach - backup)

## Notes
- Algebraic approach is preferred as it requires fewer advanced tactics
- Both strategies lead to the same conclusion but use different mathematical foundations
- The perfect square approach is more elementary and likely easier to formalize in Lean 4
