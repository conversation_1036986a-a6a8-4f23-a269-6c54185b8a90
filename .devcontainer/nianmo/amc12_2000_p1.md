# AMC12 2000 P1 Proof Tree

## Problem Statement
Find three distinct positive integers whose product is 2001 and whose sum is as large as possible.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the maximum sum of three distinct positive integers with product 2001 is 671
**Strategy**: Use prime factorization and optimization argument to show one factor must be 1, then maximize remaining pair

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Find prime factorization of 2001
2. Prove one factor must be 1 for maximum sum
3. Find optimal pair of remaining factors
4. Calculate final maximum sum
**Strategy**: Prime factorization + optimization argument + exhaustive search

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Find prime factorization of 2001
**Strategy**: Factor 2001 = 3 × 23 × 29
**Details**: Direct computation to establish 2001 = 3 × 23 × 29
**Tactic**: Use `norm_num` to prove arithmetic equality
**Proof Completion**: Successfully proved using `norm_num` tactic

### SUBGOAL_002 [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Prove one factor must be 1 for maximum sum
**Strategy**: Contradiction argument - if all factors > 1, can increase sum by setting one factor to 1
**Details**: For optimal triple (a,b,c) with a ≥ b ≥ c ≥ 1 and abc = 2001, if c > 1, replace by (ac,b,1) to get larger sum
**Failure Reason**: Complex optimization argument requires advanced inequality theory and case analysis beyond direct tactical proof. No suitable Mathlib lemmas available for this specific optimization pattern.

### SUBGOAL_002_ALT [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Directly verify that (1,3,667) gives maximum sum by exhaustive enumeration
**Strategy**: Enumerate all possible factor triples of 2001 and show (1,3,667) has maximum sum
**Details**: Since 2001 = 3 × 23 × 29, enumerate all ways to group these prime factors into 3 distinct positive integers and compare sums
**Tactic**: Use computational verification with `norm_num` and case analysis
**Failure Reason**: Implementation attempts showed that exhaustive enumeration requires complex combinatorial reasoning and complete divisibility analysis that cannot be efficiently handled by direct tactical proof methods.

### SUBGOAL_003 [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Find two distinct factors of 2001 with maximum sum
**Strategy**: Enumerate all factor pairs and compare sums
**Details**: Check pairs: (3,667), (23,87), (29,69) and find maximum sum
**Tactic**: Use `norm_num` to compute products and sums, then compare with inequalities
**Failure Reason**: Complete enumeration proof requires advanced case analysis and divisibility theory beyond direct tactical proof. After 6 attempts, unable to complete the exhaustive case verification.

### SUBGOAL_004 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Calculate final maximum sum
**Strategy**: Add 1 to the optimal pair sum
**Details**: Sum = 1 + 3 + 667 = 671
**Proof Completion**: Successfully proved using `norm_num` for arithmetic computation and distinctness verification

### SUBGOAL_005 [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Verify no other triple can exceed this sum
**Strategy**: Logical argument based on previous steps
**Details**: Since one factor must be 1 and (3,667) is optimal pair, 671 is maximum
**Failure Reason**: Requires complex optimization theory that depends on unproven SUBGOAL_002. Circular dependency makes this approach infeasible.

### SUBGOAL_FINAL [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Directly verify the main theorem by computational enumeration
**Strategy**: Enumerate all possible triples of distinct positive integers with product 2001 and verify (1,3,667) has maximum sum
**Details**: Since 2001 = 3 × 23 × 29, systematically check all ways to distribute these prime factors among 3 distinct positive integers
**Tactic**: Use finite case analysis with `norm_num` for each case
**Failure Reason**: Exhaustive enumeration requires complex combinatorial case analysis and divisibility reasoning that exceeds the scope of direct tactical proof. Multiple attempts at implementation failed due to the complexity of ensuring completeness of the enumeration.

## Current Status
- SUBGOAL_001: [PROVEN] - Prime factorization completed
- SUBGOAL_002: [DEAD_END] - Complex optimization theory required
- SUBGOAL_002_ALT: [DEAD_END] - Alternative enumeration approach failed
- SUBGOAL_003: [DEAD_END] - Exhaustive case analysis too complex
- SUBGOAL_004: [PROVEN] - Arithmetic computations completed
- SUBGOAL_005: [DEAD_END] - Circular dependency on unproven subgoals
- SUBGOAL_FINAL: [DEAD_END] - Combinatorial enumeration exceeds tactical scope

**Summary**: Significant progress made on computational aspects (prime factorization, arithmetic verification, distinctness). However, the core optimization and enumeration arguments require advanced mathematical reasoning beyond direct tactical proof methods.
