-- Proof content:
-- 1. [Problem Restatement] Given positive integers a, b, c, d with product 8! and (a+1)(b+1)=525, (b+1)(c+1)=147, (c+1)(d+1)=105, find a−d. 2. [Key Idea] Introduce x=a+1, y=b+1, z=c+1, w=d+1; solve the chain xy=525, yz=147, zw=105, enforce integrality, and check the 8! product condition. 3. [Proof] Let x=a+1, y=b+1, z=c+1, w=d+1 (so x,y,z,w≥2). The three given equations become (1) xy = 525 = 3·5²·7 (2) yz = 147 = 3·7² (3) zw = 105 = 3·5·7 Step 1. Express y, z, w in terms of x. y = 525 / x z = 147 / y = 147x / 525 = (7/25)x w = 105 / z = 105 · 25 / (7x) = 375 / x Step 2. Integers constraints. (a) z integer ⇒ 25 | x (b) w integer ⇒ x | 375 (c) x | 525 (from y integer) Hence x divides gcd(525,375)=75 and is a multiple of 25. Possible x: 25 or 75. Step 3. Test each x. • x = 25 y = 525/25 = 21 → a = 24, b = 20 z = (7/25)·25 = 7 → c = 6 w = 375/25 = 15 → d = 14 Product a·b·c·d = 24·20·6·14 = 40320 = 8! ✔ • x = 75 y = 525/75 = 7 → a = 74, b = 6 z = (7/25)·75 = 21 → c = 20 w = 375/75 = 5 → d = 4 Product = 74·6·20·4 = 35520 ≠ 8! ✘ Only the first set satisfies all conditions. Step 4. Compute a−d. a−d = 24 − 14 = 10. 4. [Conclusion] The required value is 10, answer choice (D).
