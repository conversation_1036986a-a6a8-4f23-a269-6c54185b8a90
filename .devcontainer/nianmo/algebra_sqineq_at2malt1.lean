import Mathlib.Algebra.Order.Ring.Basic
import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.Ring

-- Theorem: For all real numbers a, a(2 - a) ≤ 1
theorem algebra_sqineq_at2malt1 (a : ℝ) : a * (2 - a) ≤ 1 := by
  -- Strategy: Show that 1 - a(2 - a) = (a - 1)² ≥ 0
  -- Step 1: Expand and simplify 1 - a(2 - a)
  have h1 : 1 - a * (2 - a) = 1 - 2 * a + a ^ 2 := by
    ring

  -- Step 2: Factor the expression 1 - 2a + a²
  have h2 : 1 - 2 * a + a ^ 2 = (a - 1) ^ 2 := by
    ring

  -- Step 3: Use non-negativity of squares
  have h3 : (a - 1) ^ 2 ≥ 0 := by
    exact sq_nonneg (a - 1)

  -- Step 4: Conclude a(2 - a) ≤ 1
  linarith [h1, h2, h3]
