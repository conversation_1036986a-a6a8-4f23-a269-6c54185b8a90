# AMC12 2000 P6 Proof Tree

## Problem Statement
Pick two distinct primes from {5, 7, 11, 13, 17}; which listed number can equal (product) – (sum)?
Options: (A) 22 (B) 60 (C) 119 (D) 180 (E) 231

## Proof Tree

### ROOT_001 [ROOT]
**Goal**: Prove that among the given options, only 119 can be expressed as pq - (p+q) where p, q are distinct primes from {5, 7, 11, 13, 17}
**Parent Node**: None
**Strategy**: Multi-step verification approach using algebraic transformation, modular arithmetic, and bounds checking

### STRATEGY_001 [STRATEGY]
**Goal**: Transform the expression pq - (p+q) into a more analyzable form
**Parent Node**: ROOT_001
**Detailed Plan**: Use algebraic manipulation to rewrite pq - (p+q) = (p-1)(q-1) - 1, which reveals structural properties about the result
**Strategy**: Algebraic transformation and factorization

### SUBGOAL_001 [DEAD_END]
**Goal**: Prove the algebraic identity pq - (p+q) = (p-1)(q-1) - 1
**Parent Node**: STRATEGY_001
**Strategy**: Direct algebraic expansion and simplification
**Failure Reason**: Ring tactic not available, manual algebraic manipulation leads to complex natural number arithmetic with subtraction that causes compilation errors. The approach requires careful handling of natural number subtraction preconditions which is overly complex for this proof.

### SUBGOAL_001_ALT [DEAD_END]
**Goal**: Use computational verification approach instead of algebraic identity
**Parent Node**: STRATEGY_001
**Strategy**: Directly compute pq - (p+q) for all pairs of distinct primes from {5,7,11,13,17} and check which values match the given options
**Failure Reason**: Complex disjunctive goals from computational expansion cannot be resolved with available tactics. The norm_num and simp tactics don't automatically simplify the conditional expressions to False.

### SUBGOAL_001_ALT2 [DEAD_END]
**Goal**: Use explicit case-by-case verification with direct computation
**Parent Node**: STRATEGY_001
**Strategy**: Manually compute each pair (p,q) with p < q and verify that only (11,13) gives 119 among the options
**Failure Reason**: Rewrite tactics fail due to expression matching issues. The fin_cases approach generates complex case structures that don't align with the computational lemmas.

### SUBGOAL_001_ALT3 [DEAD_END]
**Goal**: Use simple decidable computation with direct evaluation
**Parent Node**: STRATEGY_001
**Strategy**: Define decidable predicates and use norm_num for direct evaluation without complex case analysis
**Failure Reason**: The norm_num tactic cannot automatically resolve false equalities like "22 = 23" to False. The fin_cases approach generates too many unsolved goals that require manual contradiction proofs.

### SUBGOAL_002 [TO_EXPLORE]
**Goal**: Apply modular arithmetic test to eliminate impossible candidates
**Parent Node**: STRATEGY_001
**Strategy**: Since p, q are odd primes, (p-1)(q-1) ≡ 0 (mod 4), so pq - (p+q) ≡ 3 (mod 4)

### SUBGOAL_003 [TO_EXPLORE]
**Goal**: Establish upper bound for possible values
**Parent Node**: STRATEGY_001
**Strategy**: Use maximum primes p=13, q=17 to get upper bound (13-1)(17-1) - 1 = 191

### SUBGOAL_004 [PROVEN]
**Goal**: Verify that 119 is achievable with specific prime pair
**Parent Node**: STRATEGY_001
**Strategy**: Show that p=11, q=13 gives pq - (p+q) = 143 - 24 = 119
**Proof Completion**: Successfully proven using direct computation with norm_num. The lemma achievable_119 shows that 11, 13 ∈ prime_set, 11 ≠ 13, and 119 = 11 * 13 - (11 + 13).

### SUBGOAL_005 [DEAD_END]
**Goal**: Prove that other options (22, 60, 180, 231) are impossible
**Parent Node**: STRATEGY_001
**Strategy**: Apply modular arithmetic test and bound constraints to eliminate each option
**Failure Reason**: Manual case analysis with pattern matching is too complex and has syntax errors. The cases tactic structure for Finset membership is overly complicated.

### CONCLUSION_001 [TO_EXPLORE]
**Goal**: Conclude that 119 is the unique answer
**Parent Node**: ROOT_001
**Strategy**: Combine results from all subgoals to establish uniqueness
