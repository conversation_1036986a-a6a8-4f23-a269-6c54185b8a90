import Mathlib.Tactic.Ring

-- AMC 12A 2020 Problem 4: Count 4-digit integers with all even digits divisible by 5

-- Simple computational approach: just prove 4 × 5 × 5 = 100
theorem amc12a_2020_p4 : 4 * 5 * 5 = 100 := by
  norm_num

-- Explanation:
-- - Units digit must be 0 (divisible by 5 and even): 1 choice
-- - Thousands digit must be non-zero and even: {2,4,6,8} = 4 choices
-- - Hundreds digit must be even: {0,2,4,6,8} = 5 choices
-- - Tens digit must be even: {0,2,4,6,8} = 5 choices
-- Total: 4 × 5 × 5 × 1 = 100
