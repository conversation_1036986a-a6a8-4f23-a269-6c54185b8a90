# Proof Tree: 11 divides 10ⁿ − (−1)ⁿ for every natural number n

## Node Structure

### ROOT_001 [ROOT]
- **Theorem Statement**: Prove that 11 divides 10ⁿ − (−1)ⁿ for every natural number n
- **Goal**: ∀ n : ℕ, 11 ∣ (10^n - (-1)^n)
- **Status**: [PROVEN]
- **Proof Completion**: Successfully completed using modular arithmetic approach

### STRATEGY_001 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Use direct modular arithmetic approach based on the key insight that 10 ≡ −1 (mod 11)
- **Strategy**: Modular arithmetic proof using congruence properties
- **Status**: [PROVEN]
- **Proof Completion**: All subgoals successfully completed, strategy was optimal

### SUBGOAL_001 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Establish that 10 ≡ −1 (mod 11)
- **Strategy**: Direct computation: 10 = 11 - 1 ≡ -1 (mod 11)
- **Status**: [PROVEN]
- **Proof Completion**: Used `rw [Int.ModEq]; norm_num` to prove by direct computation

### SUBGOAL_002 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Show that (10)ⁿ ≡ (−1)ⁿ (mod 11) follows from 10 ≡ −1 (mod 11)
- **Strategy**: Use power congruence property: if a ≡ b (mod m), then aⁿ ≡ bⁿ (mod m)
- **Status**: [PROVEN]
- **Proof Completion**: Used `h1.pow n` applying `Int.ModEq.pow` theorem

### SUBGOAL_003 [SUBGOAL]
- **Parent Node**: STRATEGY_001
- **Goal**: Conclude that 10ⁿ − (−1)ⁿ ≡ 0 (mod 11)
- **Strategy**: Use congruence subtraction: if a ≡ b (mod m), then a - b ≡ 0 (mod m)
- **Status**: [PROVEN]
- **Proof Completion**: Used `Int.modEq_iff_dvd` and `Int.modEq_zero_iff_dvd` with sign correction

### STRATEGY_002 [STRATEGY]
- **Parent Node**: ROOT_001
- **Detailed Plan**: Alternative induction approach as backup strategy
- **Strategy**: Mathematical induction on n
- **Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
- **Parent Node**: STRATEGY_002
- **Goal**: Base case n = 1: 10¹ − (−1)¹ = 11, divisible by 11
- **Strategy**: Direct computation and divisibility check
- **Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
- **Parent Node**: STRATEGY_002
- **Goal**: Inductive step: Assume 11 ∣ (10ᵏ − (−1)ᵏ), prove 11 ∣ (10ᵏ⁺¹ − (−1)ᵏ⁺¹)
- **Strategy**: Use inductive hypothesis and modular arithmetic manipulation
- **Status**: [TO_EXPLORE]

## Current Priority
- Primary strategy: STRATEGY_001 (modular arithmetic approach)
- Active exploration: SUBGOAL_001 (establish basic congruence)
