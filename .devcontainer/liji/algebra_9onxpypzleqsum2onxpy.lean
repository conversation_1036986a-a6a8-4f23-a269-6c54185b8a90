import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Algebra.Order.Field.Basic
import Mathlib.Analysis.SpecialFunctions.Pow.Real

-- Helper lemma: For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
lemma three_pos_harmonic_arith_ineq (a b c : ℝ) (ha : 0 < a) (hb : 0 < b) (hc : 0 < c) :
  (a + b + c) * (1 / a + 1 / b + 1 / c) ≥ 9 := by
  -- This follows from the AM-HM inequality: (a + b + c)/3 ≥ 3/(1/a + 1/b + 1/c)
  -- Rearranging: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  -- We use a direct approach: expand and apply AM-GM to the cross terms

  -- Expand the left side
  have h_expand : (a + b + c) * (1 / a + 1 / b + 1 / c) =
                  3 + (a/b + b/a) + (a/c + c/a) + (b/c + c/b) := by
    field_simp
    ring

  rw [h_expand]

  -- Apply AM-GM to each pair: x/y + y/x ≥ 2 for positive x, y
  have h_ab : a/b + b/a ≥ 2 := by
    have h_am_gm := Real.geom_mean_le_arith_mean2_weighted
      (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (0 : ℝ) ≤ 1/2)
      (div_nonneg (le_of_lt ha) (le_of_lt hb)) (div_nonneg (le_of_lt hb) (le_of_lt ha))
      (by norm_num : (1/2 : ℝ) + 1/2 = 1)
    -- This gives: (a/b)^(1/2) * (b/a)^(1/2) ≤ (1/2) * (a/b) + (1/2) * (b/a)
    -- Since (a/b)^(1/2) * (b/a)^(1/2) = 1, we get: 1 ≤ (a/b + b/a)/2
    -- Therefore: a/b + b/a ≥ 2
    have h_prod : (a/b) ^ (1/2 : ℝ) * (b/a) ^ (1/2 : ℝ) = 1 := by
      -- Use the fact that x^(1/2) * y^(1/2) = (x*y)^(1/2)
      rw [← Real.mul_rpow (div_nonneg (le_of_lt ha) (le_of_lt hb)) (div_nonneg (le_of_lt hb) (le_of_lt ha))]
      -- (a/b) * (b/a) = 1
      have h_cancel : (a/b) * (b/a) = 1 := by
        field_simp [ne_of_gt ha, ne_of_gt hb]
      rw [h_cancel, Real.one_rpow]
    rw [h_prod] at h_am_gm
    -- From h_am_gm: 1 ≤ 1/2 * (a/b) + 1/2 * (b/a) = (a/b + b/a)/2
    -- Therefore: 2 ≤ a/b + b/a
    have h_rearrange : 1/2 * (a/b) + 1/2 * (b/a) = (a/b + b/a) / 2 := by ring
    rw [h_rearrange] at h_am_gm
    rw [le_div_iff₀ (by norm_num : (0 : ℝ) < 2)] at h_am_gm
    norm_num at h_am_gm
    exact h_am_gm

  have h_ac : a/c + c/a ≥ 2 := by
    have h_am_gm := Real.geom_mean_le_arith_mean2_weighted
      (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (0 : ℝ) ≤ 1/2)
      (div_nonneg (le_of_lt ha) (le_of_lt hc)) (div_nonneg (le_of_lt hc) (le_of_lt ha))
      (by norm_num : (1/2 : ℝ) + 1/2 = 1)
    have h_prod : (a/c) ^ (1/2 : ℝ) * (c/a) ^ (1/2 : ℝ) = 1 := by
      rw [← Real.mul_rpow (div_nonneg (le_of_lt ha) (le_of_lt hc)) (div_nonneg (le_of_lt hc) (le_of_lt ha))]
      have h_cancel : (a/c) * (c/a) = 1 := by
        field_simp [ne_of_gt ha, ne_of_gt hc]
      rw [h_cancel, Real.one_rpow]
    rw [h_prod] at h_am_gm
    have h_rearrange : 1/2 * (a/c) + 1/2 * (c/a) = (a/c + c/a) / 2 := by ring
    rw [h_rearrange] at h_am_gm
    rw [le_div_iff₀ (by norm_num : (0 : ℝ) < 2)] at h_am_gm
    norm_num at h_am_gm
    exact h_am_gm

  have h_bc : b/c + c/b ≥ 2 := by
    have h_am_gm := Real.geom_mean_le_arith_mean2_weighted
      (by norm_num : (0 : ℝ) ≤ 1/2) (by norm_num : (0 : ℝ) ≤ 1/2)
      (div_nonneg (le_of_lt hb) (le_of_lt hc)) (div_nonneg (le_of_lt hc) (le_of_lt hb))
      (by norm_num : (1/2 : ℝ) + 1/2 = 1)
    have h_prod : (b/c) ^ (1/2 : ℝ) * (c/b) ^ (1/2 : ℝ) = 1 := by
      rw [← Real.mul_rpow (div_nonneg (le_of_lt hb) (le_of_lt hc)) (div_nonneg (le_of_lt hc) (le_of_lt hb))]
      have h_cancel : (b/c) * (c/b) = 1 := by
        field_simp [ne_of_gt hb, ne_of_gt hc]
      rw [h_cancel, Real.one_rpow]
    rw [h_prod] at h_am_gm
    have h_rearrange : 1/2 * (b/c) + 1/2 * (c/b) = (b/c + c/b) / 2 := by ring
    rw [h_rearrange] at h_am_gm
    rw [le_div_iff₀ (by norm_num : (0 : ℝ) < 2)] at h_am_gm
    norm_num at h_am_gm
    exact h_am_gm

  -- Combine the inequalities: 3 + 2 + 2 + 2 = 9
  linarith [h_ab, h_ac, h_bc]

-- Theorem: For all positive real numbers x, y, z: 9/(x + y + z) ≤ 2/(x + y) + 2/(y + z) + 2/(z + x)
theorem algebra_9onxpypzleqsum2onxpy (x y z : ℝ) (hx : 0 < x) (hy : 0 < y) (hz : 0 < z) :
  9 / (x + y + z) ≤ 2 / (x + y) + 2 / (y + z) + 2 / (z + x) := by
  -- Direct proof using the well-known inequality
  -- For positive a, b, c: (a + b + c)(1/a + 1/b + 1/c) ≥ 9
  have pos_xy : 0 < x + y := add_pos hx hy
  have pos_yz : 0 < y + z := add_pos hy hz
  have pos_zx : 0 < z + x := add_pos hz hx
  have pos_sum : 0 < x + y + z := add_pos (add_pos hx hy) hz

  -- Apply the helper lemma
  have key_ineq : ((x + y) + (y + z) + (z + x)) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 :=
    three_pos_harmonic_arith_ineq (x + y) (y + z) (z + x) pos_xy pos_yz pos_zx

  -- Note that (x+y) + (y+z) + (z+x) = 2(x+y+z)
  have sum_eq : (x + y) + (y + z) + (z + x) = 2 * (x + y + z) := by ring
  rw [sum_eq] at key_ineq

  -- From key_ineq: 2(x+y+z) * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9
  -- Divide by (x+y+z): 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) ≥ 9/(x+y+z)
  have h_div : 2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) ≥ 9 / (x + y + z) := by
    -- Use the fact that a * b / a = b for a ≠ 0
    have h_simp : 2 * (x + y + z) * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)) =
                  (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := by ring
    rw [h_simp] at key_ineq
    -- Now we have: (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) ≥ 9
    -- This means: 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x)))
    -- Dividing both sides by (x + y + z) > 0 gives us the desired result
    -- We use the fact that if a * b ≥ c and a > 0, then b ≥ c / a
    have h_ge : 9 ≤ (x + y + z) * (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) := key_ineq
    -- Apply division: if a * b ≥ c and a > 0, then b ≥ c / a
    have h_div_rule : ∀ (a b c : ℝ), a > 0 → a * b ≥ c → b ≥ c / a := by
      intros a b c ha hab
      -- We have a * b ≥ c, want to show b ≥ c / a
      -- Note that b ≥ c / a is the same as c / a ≤ b
      -- By div_le_iff₀: c / a ≤ b ↔ c ≤ b * a
      have h_goal : b ≥ c / a ↔ c ≤ b * a := by
        rw [ge_iff_le, div_le_iff₀ ha]
      rw [h_goal]
      -- We have a * b ≥ c, which is c ≤ a * b, which is c ≤ b * a
      rw [mul_comm] at hab
      exact hab
    exact h_div_rule (x + y + z) (2 * (1 / (x + y) + 1 / (y + z) + 1 / (z + x))) 9 pos_sum h_ge

  -- Expand: 2 * (1/(x+y) + 1/(y+z) + 1/(z+x)) = 2/(x+y) + 2/(y+z) + 2/(z+x)
  rw [mul_add, mul_add, mul_one_div, mul_one_div, mul_one_div] at h_div
  exact h_div
