import Mathlib.Algebra.Order.Ring.Pow
import Mathlib.Data.Real.Basic
import Mathlib.Tactic.Linarith

-- <PERSON><PERSON><PERSON>'s Inequality: (1 + nx) ≤ (1 + x)ⁿ for n ≥ 1 and x > -1
theorem bernoulli_inequality (n : ℕ) (x : ℝ) (hn : n ≥ 1) (hx : x > -1) :
  1 + n * x ≤ (1 + x) ^ n := by
  -- Use the existing <PERSON><PERSON><PERSON> inequality from Mathlib
  -- We need to show that x ≥ -2 to apply one_add_mul_le_pow
  have h_ge_neg_two : -2 ≤ x := by l<PERSON><PERSON> [hx]
  exact one_add_mul_le_pow h_ge_neg_two n
