# IMO 2019 P1 Proof Tree

## Problem Statement
Find all functions f : ℤ → ℤ that satisfy f(2a) + 2f(b) = f(f(a + b)) for every pair of integers a, b.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that the only solutions are f(x) ≡ 0 and f(x) = 2x + c for arbitrary integer c
**Strategy**: Use substitution method to derive functional properties and reduce to Cauchy equation

### STRATEGY_001 [STRATEGY]
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Use strategic substitutions (a=0, b=0) to isolate f(0) and derive key relations
2. Transform the original equation into additive form
3. Reduce to Cauchy equation and solve
4. Verify solutions satisfy original equation
**Strategy**: Substitution and reduction approach

### SUBGOAL_001 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Isolate f(0) = c and derive f(2a) = 2f(a) - c
**Strategy**: Put a = 0 and b = 0 in original equation to get two key relations
**Details**:
- Put a = 0: c + 2f(b) = f(f(b)) ∀ b (equation 1)
- Put b = 0: f(2a) + 2c = f(f(a)) ∀ a (equation 2)
- Combine to get f(2a) = 2f(a) - c
**Proof Completion**: Successfully implemented eq1, eq2, and eq3 using direct substitution and algebraic manipulation

### SUBGOAL_002 [PROVEN]
**Parent Node**: STRATEGY_001
**Goal**: Transform original equation to additive form f(a + b) = f(a) + f(b) - c
**Strategy**: Substitute derived relations back into original equation
**Details**: Use equations (1) and (3) from SUBGOAL_001 to simplify original equation
**Proof Completion**: Successfully derived eq4 using substitution of eq1 and eq3 into original equation

### SUBGOAL_003 [DEAD_END]
**Parent Node**: STRATEGY_001
**Goal**: Solve Cauchy equation g(a + b) = g(a) + g(b) where g(x) = f(x) - c
**Strategy**: Apply standard Cauchy equation solution on integers
**Details**: Since g satisfies additivity on ℤ, we have g(x) = dx for some integer d
**Failure Reason**: Complex induction proof on integers causing compilation errors, need simpler direct approach

### SUBGOAL_003_ALT [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Solve Cauchy equation using direct construction
**Strategy**: Use g(1) to define linear function and prove equality
**Details**: Define d = g(1) and prove g(x) = d*x directly using additivity properties

### SUBGOAL_004 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Determine constraints on d and c by substituting f(x) = dx + c back into original equation
**Strategy**: Coefficient comparison method
**Details**:
- Substitute f(x) = dx + c into f(2a) + 2f(b) = f(f(a + b))
- Compare coefficients to get d(d-2) = 0 and (d-2)c = 0
- Solve system to get d = 0 or d = 2

### SUBGOAL_005 [TO_EXPLORE]
**Parent Node**: STRATEGY_001
**Goal**: Verify that f(x) ≡ 0 and f(x) = 2x + c are the only solutions
**Strategy**: Direct verification by substitution
**Details**: Check both solution families satisfy the original functional equation

## Current Status
- Total nodes: 6
- TO_EXPLORE: 5
- PROVEN: 0
- DEAD_END: 0
