import Mathlib.Data.Real.Basic
import Mathlib.Analysis.MeanInequalities
import Mathlib.Analysis.SpecialFunctions.Log.Basic
import Mathlib.Data.Finset.Basic

-- Theorem: Given non-negative reals a₁,...,aₙ with a₁+⋯+aₙ = n, prove that a₁a₂⋯aₙ ≤ 1
theorem amgm_sum_to_product_bound (n : ℕ) (hn : 0 < n) (a : Fin n → ℝ)
  (h_nonneg : ∀ i, 0 ≤ a i) (h_sum : ∑ i, a i = n) :
  ∏ i, a i ≤ 1 := by
  -- Strategy 3: Direct case analysis
  -- SUBGOAL_006: Handle case where some a_i = 0
  by_cases h_zero : ∃ i, a i = 0
  · -- Case: some a_i = 0, so product is 0 ≤ 1
    obtain ⟨i, hi⟩ := h_zero
    rw [Finset.prod_eq_zero (Finset.mem_univ i)]
    · exact zero_le_one
    · exact hi
  · -- Case: all a_i > 0
    -- SUBGOAL_007: Handle case where all a_i > 0
    push_neg at h_zero
    have h_pos : ∀ i, 0 < a i := by
      intro i
      exact lt_of_le_of_ne (h_nonneg i) (h_zero i).symm
    -- Use AM-GM inequality: (a₁ + ... + aₙ)/n ≥ (a₁ · ... · aₙ)^(1/n)
    -- Since a₁ + ... + aₙ = n, we get 1 ≥ (a₁ · ... · aₙ)^(1/n)
    -- Therefore a₁ · ... · aₙ ≤ 1
    have n_cast_pos : (0 : ℝ) < n := Nat.cast_pos.mpr hn
    have prod_pos : 0 < ∏ i, a i := Finset.prod_pos (fun i _ => h_pos i)
    -- Apply Real.geom_mean_le_arith_mean_weighted with uniform weights 1/n
    have h_weights : ∀ i ∈ (@Finset.univ (Fin n) _), 0 ≤ (1 : ℝ) / n := by
      intro i _
      exact div_nonneg zero_le_one (le_of_lt n_cast_pos)
    have h_weight_sum : ∑ i ∈ (@Finset.univ (Fin n) _), (1 : ℝ) / n = 1 := by
      rw [Finset.sum_const, Finset.card_univ, Fintype.card_fin]
      simp only [nsmul_eq_mul]
      rw [mul_div_cancel₀]
      exact ne_of_gt n_cast_pos
    have h_nonneg_mem : ∀ i ∈ (@Finset.univ (Fin n) _), 0 ≤ a i := by
      intro i _
      exact h_nonneg i
    have h_amgm := Real.geom_mean_le_arith_mean_weighted (@Finset.univ (Fin n) _) (fun _ => (1 : ℝ) / n) a h_weights h_weight_sum h_nonneg_mem
    -- Simplify the AM-GM result
    have lhs_eq : ∏ i ∈ (@Finset.univ (Fin n) _), a i ^ ((1 : ℝ) / n) = (∏ i, a i) ^ (1 / n : ℝ) := by
      -- Use Real.finset_prod_rpow since we're dealing with real powers
      exact Real.finset_prod_rpow (@Finset.univ (Fin n) _) a h_nonneg_mem (1 / n)
    -- Simplify h_amgm directly using the constraint
    -- We have: ∏ i, a i ^ (1/n) ≤ ∑ i, (1/n) * a i = (1/n) * ∑ i, a i = (1/n) * n = 1
    have h_sum_weights_times_a : ∑ i ∈ (@Finset.univ (Fin n) _), (1 : ℝ) / n * a i = 1 := by
      -- This follows from ∑ i, (1/n) * a i = (1/n) * ∑ i, a i = (1/n) * n = 1
      rw [← Finset.mul_sum]
      rw [h_sum]
      rw [div_mul_cancel₀]
      exact ne_of_gt n_cast_pos
    -- And ∏ i, a i ^ (1/n) = (∏ i, a i) ^ (1/n) by Finset.prod_pow_eq_pow_sum
    have h_prod_pow : ∏ i ∈ (@Finset.univ (Fin n) _), a i ^ ((1 : ℝ) / n) = (∏ i, a i) ^ (1 / n : ℝ) := by
      -- This follows from the general property ∏ i, x_i^c = (∏ i, x_i)^c
      -- Use Real.finset_prod_rpow since we're dealing with real powers
      exact Real.finset_prod_rpow (@Finset.univ (Fin n) _) a h_nonneg_mem (1 / n)
    -- Combine to get (∏ i, a i) ^ (1/n) ≤ 1
    rw [h_prod_pow, h_sum_weights_times_a] at h_amgm
    -- Use rpow_le_rpow_iff to conclude ∏ i, a i ≤ 1
    have h_one_div_n_pos : (0 : ℝ) < 1 / n := div_pos zero_lt_one n_cast_pos
    have h_prod_nonneg : 0 ≤ ∏ i, a i := le_of_lt prod_pos
    -- From (∏ i, a i) ^ (1/n) ≤ 1, conclude ∏ i, a i ≤ 1
    -- Use Real.rpow_le_rpow_iff: x^z ≤ y^z ↔ x ≤ y when x,y ≥ 0 and z > 0
    have h_rpow_iff : (∏ i, a i) ^ (1 / n : ℝ) ≤ (1 : ℝ) ^ (1 / n : ℝ) ↔ ∏ i, a i ≤ 1 :=
      Real.rpow_le_rpow_iff h_prod_nonneg zero_le_one h_one_div_n_pos
    rw [Real.one_rpow] at h_rpow_iff
    exact h_rpow_iff.mp h_amgm
